import WPAPI from "wpapi";
import axios from "axios";

let dupes= [];
let no_dupes = [];
let output_error = [];

const wp = new WPAPI({
    endpoint: "https://vaumc.org/wp-json",
    username: "<EMAIL>",
    password: "ksNl*@z1@Zfc1XBj",
});

// Register custom routes if needed
wp.church = wp.registerRoute("wp/v2", "/church/(?P<id>\\d+)");

async function fetchChurches() {
    let churches = [];
    let page = 1;

    try {
        while (true) {
            const response = await wp
                .church()
                .param("per_page", 100)
                .param("page", page)
                .param("status", ["publish", "draft"]); // Include both published and draft posts

            if (response.length === 0) break;
            churches = churches.concat(response);
            page++;
        }
    } catch (error) {
        console.error("Error fetching churches:", error.message);
    }

    return churches;
}

async function findChurchByChurchId(id,status) {
    const url = `https://vaumc.org/wp-json/yobo/v2/church/?church_id=${id}`;
  
    try {
      console.log("Finding church by ID:", id);
      const response = await axios.get(url);
      const data = response.data;
      //console.log("Found church data:", data);
      if(data.length > 1){
        dupes.push(id,status)
        return data
      }else{
        no_dupes.push(id)
        return null
      }
    } catch (error) {
      console.error("Error finding Church by ChurchId:", error);
      throw error;
    }
  }

async function removeDuplicates(id,status){
    try{
        const existingChurch = await findChurchByChurchId(id,status)
        if (existingChurch){
            console.log(existingChurch)
        }else{
            return
        }
    }catch(err){
        console.log(err);
    }
}

async function main() {
    const churches = await fetchChurches();

    for (const church of churches) {
        await removeDuplicates(church.acf.ChurchId,church.acf.Status);
    }
    console.log("dupes:", dupes.length, dupes);
    console.log("No dupes:", no_dupes.length);
    console.error("Errors:", output_error);
}

main();