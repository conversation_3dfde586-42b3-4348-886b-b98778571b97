import axios from "axios";
import * as qs from "qs";
import WPAPI from 'wpapi';

let output_not_updated: string[] = [];
let output_success: string[] = [];
let output_error: string[] = [];

type Church = {
  ChurchId: number;
  Name: string | null;
  Status: string | null;
  GCFA: string | null;
  Charge: string | null;
  District: string | null;
  Address: string | null;
  City: string | null;
  State: string | null;
  ZipCode: string | null;
  HeadPastor: string | null;
  Phone: string | null;
  Email: string | null;
  Website: string | null;
  Facebook: string | null;
  Youtube: string | null;
  Twitter: string | null;
  Instagram: string | null;
  Vimeo: string | null;
  LastUpdated: string;
  ImageLastUpdated: string | null;
};

WPAPI.prototype.url = function( url ) {
    console.log(url);
	return new WPRequest( {
		...WPAPI._options,
		endpoint: url,
	} );
};

const wp = new WPAPI({
  endpoint: "https://vaumc.org/wp-json",
  username: "<EMAIL>",
  password: "ksNl*@z1@Zfc1XBj",
});

wp.church = wp.registerRoute("wp/v2", "/church/(?P<id>\\d+)", {
  params: ["acf[ChurchId]"],
});

async function getToken() {
    console.log("Getting Access Token...");
    const url = "https://unityapi.vaumc.org/token";
    
    // Use qs.stringify to format the data correctly
    const data = qs.stringify({
      userName: "YoboClergyDirectory",
      password: "CirclingWatermelonZoo",
      grant_type: "password",
    });
    
    try {
      // Change to axios.post and pass data as the second argument
      const response = await axios.post(url, data, {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      });
      
      console.log("Access Token:", response.data.access_token);
      return response.data.access_token;
    } catch (error) {
      console.error("Error fetching token:", error);
      throw error;
    }
  }

async function getChurchDirectory(token: string): Promise<Church[]> {
  console.log("Getting Church Directory...");
  const url = "https://unityapi.vaumc.org/api/ChurchDirectory";
  const response = await axios.get(url, {
    headers: { Authorization: `Bearer ${token}` },
  });
  console.log("Got Church Directory Length:", response.data.length);
  return response.data;
}

async function findChurchByChurchId(id: number) {
  const url = `https://vaumc.org/wp-json/yobo/v2/church/?church_id=${id}`;

  try {
    const response = await fetch(url);

    // Check if the response is OK (status code 200-299)
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();

    // Assuming the endpoint returns an array of clergy and you need the first one
    return data.length > 0 ? data[0] : null;
  } catch (error) {
    console.error("Error finding Church by ChurchId:", error);
    throw error;
  }
}

async function getChurchImage(token: string, churchId: number) {
  const url = `https://unityapi.vaumc.org/api/ChurchImage?churchid=${churchId}`;
  try {
    const response = await axios.get(url, {
      headers: { Authorization: `Bearer ${token}` },
      responseType: "json", // Set responseType to 'json'
    });

    // Extracting the base64 part of the image
    if (response.data && response.data.Image) {
      const base64Image = response.data.Image.split(";base64,").pop();
      // Check if the Image data is empty or contains a specific message
      if (
        !response.data.Image ||
        response.data.Image === "Sequence contains no elements"
      ) {
        return 0;
      }
      // Converting the base64 string to a buffer
      const imageBuffer = Buffer.from(base64Image, "base64");
      return imageBuffer;
    } else {
      return 0;
    }
  } catch (error) {
    console.error("Error fetching clergy image:", error);
    throw error;
  }
}

async function insertOrUpdateChurch(church: Church, token: string) {
  let postData;

  try {
    // const yesterday = new Date();
    // yesterday.setDate(yesterday.getDate() - 1);

    // if (new Date(church.LastUpdated) < yesterday) {
    //   output_not_updated.push(
    //     `church not updated since yesterday: ${church.Name} ${church.LastUpdated}`
    //   );
    //   console.log(
    //     "church not updated since yesterday:",
    //     `${church.Name}`,
    //     church.LastUpdated
    //   );
    //   return;
    // }

    // Check if church member already exists in WP
    const existingChurch = await findChurchByChurchId(church.ChurchId);

    let existsInWP = false;
    if (
      existingChurch &&
      typeof existingChurch === "object" &&
      "acf" in existingChurch
    ) {
      existsInWP = true;
    }

    if (existsInWP && church.Name !== existingChurch?.acf?.Name) {
      console.log(
        "ERROR! church name does not match",
        `${church.Name}`,
        `${existingChurch?.acf?.Name}`
      );
      output_error.push(
        `ERROR! church name does not match, ${church.Name}, ${existingChurch?.acf?.Name}`
      );
      return;
    }

    const action = existsInWP ? "Updating" : "Inserting a new";
    console.log(`${action} church:`, church.Name, church.LastUpdated);
    output_success.push(
      `${action} church: ${church.Name} ${church.LastUpdated}`
    );

    // Upload image if available
    let media;
    const imageBlob = await getChurchImage(token, church.ChurchId);
    if (imageBlob) {
      // Convert Buffer to Blob
      const blob = new Blob([imageBlob], { type: "image/jpeg" });
      const file = new File([blob], `${church.ChurchId}.jpg`, {
        type: "image/jpeg",
      });

      media = await wp
        .media()
        .file(file) // Use the File object here
        .create({
          title: `${church.Name}_featured_image`,
          alt_text: `${church.Name}_featured_image`,
          caption: `${church.Name} Featured Image`,
          description: `${church.Name} Featured Image`,
        });
    } else {
      media = { id: 31490 };
    }

    postData = {
      title: church.Name,
      acf: {
        ChurchId: church.ChurchId,
        Name: church.Name || null,
        Status: church.Status || null,
        GCFA: church.GCFA || null,
        Charge: church.Charge || null,
        District: church.District || null,
        Address: church.Address || null,
        City: church.City || null,
        State: church.State || null,
        ZipCode: church.ZipCode || null,
        HeadPastor: church.HeadPastor || null,
        Phone: church.Phone || null,
        Email: church.Email || null,
        Website: church.Website || null,
        Facebook: church.Facebook || null,
        Youtube: church.Youtube || null,
        Twitter: church.Twitter || null,
        Instagram: church.Instagram || null,
        Vimeo: church.Vimeo || null,
        LastUpdated: church.LastUpdated || null,
        ImageLastUpdated: church.ImageLastUpdated || null,
      },
      featured_media: media?.id,
      status: "publish",
    };
    const response = existsInWP
      ? await wp.church().id(existingChurch.post.ID).update(postData)
      : await wp.church().create(postData);

    return response;
  } catch (error) {
    console.error("Error inserting or updating church:", error, postData);
    throw error;
  }
}

async function main() {
  const token = await getToken();
  console.log('got token', token)
  const churchDirectory = await getChurchDirectory(token);

  for (const church of churchDirectory) {
    try {
      const result = await insertOrUpdateChurch(church, token);
    } catch (error) {
      console.error("An error occured:", error);
    }
  }
  console.log("Completed Inserts");
  console.log(output_not_updated);
  console.log(output_success);
  console.log(output_error);
}

main();
