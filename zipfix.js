import WPAPI from "wpapi";

let output_not_updated = [];
let output_success = [];
let output_error = [];

const wp = new WPAPI({
    endpoint: "https://vaumc.org/wp-json",
    username: "<EMAIL>",
    password: "ksNl*@z1@Zfc1XBj",
});

// Register custom routes if needed
wp.church = wp.registerRoute("wp/v2", "/church/(?P<id>\\d+)");

// Fetch all churches
async function fetchChurches() {
    let churches = [];
    let page = 1;

    try {
        while (true) {
            const response = await wp.church().param("per_page", 100).param("page", page);
            if (response.length === 0) break;
            churches = churches.concat(response);
            page++;
        }
    } catch (error) {
        console.error("Error fetching churches:", error.message);
    }

    return churches;
}

// Update ZipCode for a church
async function updateZipCode(church) {
    const zipCode = church.acf?.ZipCode;

    // Check if ZipCode ends with a dash
    if (!zipCode || !zipCode.endsWith("-")) {
        output_not_updated.push({ id: church.id, name: church.title.rendered });
        return;
    }

    // Clean up the ZipCode
    const updatedZipCode = zipCode.replace(/-$/, ""); // Remove the trailing dash

    try {
        console.log(`${wp.endpoint}/wp/v2/church/${church.id}`)
        wp.church().id(church.id).update(
            {
                acf:
                {
                    ZipCode: updatedZipCode
                }
            }
        )
        output_success.push({ id: church.id, name: church.title.rendered, newZipCode: updatedZipCode });
    } catch (error) {
        output_error.push({
            id: church.id,
            name: church.title.rendered,
            error: error.response?.data || error.message,
        });
    }
}

// Main function
async function main() {
    const churches = await fetchChurches();

    for (const church of churches) {
        await updateZipCode(church);
    }
    console.log("Successfully updated:", output_success);
    console.log("No updates needed:", output_not_updated);
    console.error("Errors:", output_error);
}

main();
