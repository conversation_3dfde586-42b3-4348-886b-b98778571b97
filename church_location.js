import WPAPI from "wpapi";
import axios from "axios"; 

let output_not_updated = [];
let output_success = [];
let output_error = [];

const apiUrl = `https://vaumc.org/wp-json/simple-locator/v1/update-meta/`
const wp = new WPAPI({
    endpoint: "https://vaumc.org/wp-json",
    username: "<EMAIL>",
    password: "ksNl*@z1@Zfc1XBj",
});

// Register custom routes if needed
wp.church = wp.registerRoute("wp/v2", "/church/(?P<id>\\d+)");

// Fetch all churches
async function fetchChurches() {
    let churches = [];
    let page = 1;

    try {
        while (true) {
            const response = await wp.church().param("per_page", 100).param("page", page);
            if (response.length === 0) break;
            churches = churches.concat(response);
            page++;
        }
    } catch (error) {
        console.error("Error fetching churches:", error.message);
    }

    return churches;
}

// Update ZipCode for a church
// Update ZipCode for a church
async function setLocation(church) {
    let wpsl_data = {
        wpsl_address: church.acf.Address,
        wpsl_city: church.acf.City,
        wpsl_state: church.acf.State,
        wpsl_zip: church.acf.ZipCode,
        wpsl_country: "United States",
        wpsl_phone: church.acf.Phone ? church.acf.Phone : null,
        wpsl_website: church.acf.Website ? church.acf.Website : null
    };

    try {
        // Correct the path format for the update request
        console.log('trying to update wpsl: ', church.id, church.acf.Name)
        const response = await axios.post(`${apiUrl}${church.id}`, wpsl_data, {
            auth: {
                username: '<EMAIL>',
                password: 'ksNl*@z1@Zfc1XBj'
            },
            headers: {
                'Content-Type': 'application/json', // Set content type to JSON
            },
        });
        
        output_success.push({ id: church.id, name: church.acf.Name, wpsl_data });
    } catch (error) {
        console.log('error updating wpsl: ', church.id, church.acf.Name)
        console.error(error)
        output_error.push({
            id: church.id,
            name: church.acf.Name,
            error: error.response?.data || error.message
        });
    }
}


// Main function
async function main() {
    const churches = await fetchChurches();

    for (const church of churches) {
        if(church.acf.Status === "Active"){
            await setLocation(church);
        }
    }
    console.log("Successfully updated:", output_success);
    console.log("No updates needed:", output_not_updated);
    console.error("Errors:", output_error);
}

main();