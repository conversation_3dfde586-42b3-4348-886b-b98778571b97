import axios from "axios";
import * as qs from "qs";
import WPAPI from "wpapi";

let output_not_updated = [];
let output_success = [];
let output_error = [];

const wp = new WPAPI({
  endpoint: "https://vaumc.org/wp-json",
  username: "<EMAIL>",
  password: "ksNl*@z1@Zfc1XBj",
});
const apiUrl = `https://vaumc.org/wp-json/simple-locator/v1/update-meta`

// Register custom routes if needed
wp.church = wp.registerRoute("wp/v2", "/church/(?P<id>\\d+)");
wp.districtTerm = wp.registerRoute("wp/v2", "/district/(?P<id>\\d+)");

async function getToken() {
  console.log("Getting Access Token...");
  const url = "https://unityapi.vaumc.org/token";

  const data = qs.stringify({
    userName: "YoboClergyDirectory",
    password: "CirclingWatermelonZoo",
    grant_type: "password",
  });

  try {
    const response = await axios.post(url, data, {
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
    });

    console.log("Access Token:", response.data.access_token);
    return response.data.access_token;
  } catch (error) {
    console.error("Error fetching token:", error);
    throw error;
  }
}

async function getChurchDirectory(token) {
  console.log("Getting Church Directory...");
  const url = "https://unityapi.vaumc.org/api/ChurchDirectory";
  const response = await axios.get(url, {
    headers: { Authorization: `Bearer ${token}` },
  });
  console.log("Got Church Directory Length:", response.data.length);
  return response.data;
}

async function findChurchByChurchId(id) {
  const url = `https://vaumc.org/wp-json/yobo/v2/church/?church_id=${id}`;

  try {
    console.log("Finding church by ID:", id);
    const response = await axios.get(url);
    const data = response.data;
    //console.log("Found church data:", data);

    return data.length > 0 ? data[0] : null;
  } catch (error) {
    console.error("Error finding Church by ChurchId:", error);
    throw error;
  }
}
async function updateOrSetLocation(church,id){
  let wpsl_data = {
    wpsl_address: church.Address,
    wpsl_city: church.City,
    wpsl_state: church.State,
    wpsl_zip: church.ZipCode,
    wpsl_country: "United States",
    wpsl_phone: church.Phone ? church.Phone : null,
    wpsl_website: church.Website ? church.Website : null
};

try {
    // Correct the path format for the update request
    console.log('trying to update wpsl: ', id, church.Name)
    const response = await axios.post(`${apiUrl}/${id}`, wpsl_data, {
        auth: {
            username: '<EMAIL>',
            password: 'ksNl*@z1@Zfc1XBj'
        },
        headers: {
            'Content-Type': 'application/json', // Set content type to JSON
        },
    });
    
    output_success.push({ id: id, name: church.Name, wpsl_data });
} catch (error) {
    console.log('error updating wpsl: ', id, church.Name)
    console.error(error)
    output_error.push({
        id: id,
        name: church.Name,
        error: error.response?.data || error.message
    });
}
}

async function insertOrUpdateChurch(church, token) {
  let postData;

  try {
    // console.log("Processing church:", church);
    const existingChurch = await findChurchByChurchId(church.ChurchId);
    let existsInWP = false;

    if (
      existingChurch &&
      typeof existingChurch === "object" &&
      "acf" in existingChurch
    ) {
      existsInWP = true;
    }

    if (existsInWP) {
      const lastUpdated = existingChurch.acf.LastUpdated;

      if (new Date(church.LastUpdated) <= new Date(lastUpdated)) {
        // console.log(
        //   `Skipping church: ${church.Name}, as its LastUpdated (${church.LastUpdated}) is older or the same as WordPress data (${lastUpdated}).`
        // );
        output_success.push(
          `Skipped church: ${church.Name}, LastUpdated (${church.LastUpdated}) hasn't changed.`
        );
        return;
      }

      if (church.Name !== existingChurch.acf.Name) {
        console.log(
          "ERROR! church name does not match",
          `${church.Name}`,
          `${existingChurch.acf.Name}`
        );
        output_error.push(
          `ERROR! church name does not match, ${church.Name}, ${existingChurch.acf.Name}`
        );
        return;
      }
    }


    const action = existsInWP ? "Updating" : "Inserting a new";
    console.log(`${action} church:`, church.Name, church.LastUpdated);


    // Ensure District term exists or create it
    let districtId = church.District;
    if (districtId) {
      const existingDistrict = await findOrCreateDistrictTerm(districtId);
      districtId = existingDistrict.id; // Get the created or existing term ID
    }

    console.log('District ID:', districtId);  // Log the districtId to ensure it's valid

    // Handle image upload and set media
    let media = { id: 53073 }; // Default to a placeholder media ID
    try {
      if (!existsInWP || (new Date(church.ImageLastUpdated) > new Date(existingChurch.acf.ImageLastUpdated))) {
        console.log(
          `Processing image for church: ${church.Name}. ${existsInWP ? "Updating image..." : "Uploading new image..."
          }`
        );

        // If this is an existing church, remove the old featured image
        if (existsInWP && existingChurch.featured_media) {
          try {
            await wp.media().id(existingChurch.featured_media).delete();
            console.log(
              `Removed old image for church: ${church.Name} (Media ID: ${existingChurch.featured_media}).`
            );
          } catch (error) {
            console.error(`Failed to remove old image for church: ${church.Name}`, error.message);
          }
        }

        // Upload the new image
        const imageBlob = await getChurchImage(token, church.ChurchId);
        if (imageBlob) {
          media = await wp.media()
            .file(imageBlob, `${church.ChurchId}.jpg`)
            .create({
              title: `${church.Name}`,
              alt_text: `${church.Name}`,
              caption: `${church.Name}`,
              description: `${church.Name}`,
            });
        }
      } else {
        console.log(
          `Skipping image upload for church: ${church.Name}, as the ImageLastUpdated (${church.ImageLastUpdated}) is not newer than WordPress data (${existingChurch.acf.ImageLastUpdated}).`
        );
      }
    } catch (error) {
      console.error("Media Upload Error:", error.message);
    }
    let email = church.Email ? church.Email.trim() : church.Email 
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (!emailRegex.test(email)) {
      console.error("Invalid email format:", email, church.name, church.ChurchId);
      email = null
    }

    let zipcode = church.ZipCode
    if(zipcode && zipcode.endsWith("-")){
      zipcode = zipcode.replace(/-$/, "")
    }
    let status = church.Status === "Active" ? "publish" : "draft"
    postData = {
      title: church.Name,
      district: districtId ? districtId : null,
      acf: { // Populate ACF fields
        ChurchId: church.ChurchId || null,
        Name: church.Name || null,
        Status: church.Status || null,
        GCFA: church.GCFA || null,
        Charge: church.Charge || null,
        District: districtId ? districtId : null,  // Use null if no districtId
        Address: church.Address || null,
        City: church.City || null,
        State: church.State || null,
        ZipCode: zipcode || null,
        HeadPastor: church.HeadPastor || null,
        Phone: church.Phone || null,
        Email: email || null,
        Website: church.Website || null,
        Facebook: church.Facebook || null,
        Youtube: church.Youtube || null,
        Twitter: church.Twitter || null,
        Instagram: church.Instagram || null,
        Vimeo: church.Vimeo || null,
        LastUpdated: church.LastUpdated || null,
        ImageLastUpdated: church.ImageLastUpdated || null,
      },
      featured_media: media.id || 53073, // Set feature image if available
      status: status,
    };

    console.log("Post Data to be sent:", postData);
    const response = existsInWP
      ? await wp.church().id(existingChurch.post.ID).update(postData)
      : await wp.church().create(postData);
    
    if(!existsInWP){
      let id = response.id
      updateOrSetLocation(church,id)
    }else{
        if(church.Address !== existingChurch.acf.Address){
          updateOrSetLocation(church, existingChurch.post.id)
        }
    }
    //console.log(`Successfully ${action.toLowerCase()} church:`, response);
    output_success.push(
      `${action} church: ${church.Name} ${church.LastUpdated}`
    );
    return response;

  } catch (error) {
    console.error("Error inserting or updating church:", error, postData);
    output_error.push(`Error inserting or updating church ${church.Name} - ${church.ChurchId}: ${error.message}`);
    throw error;
  }
}

async function findOrCreateDistrictTerm(districtName) {
  try {
    const existingTerms = await wp.districtTerm().param({ search: districtName }).get();
    if (existingTerms && existingTerms.length > 0) {
      return existingTerms[0]; // Return the existing term
    }
    const newTerm = await wp.districtTerm().create({ name: districtName });
    return newTerm; // Return the newly created term
  } catch (error) {
    console.error(`Error finding or creating district term: ${error.message}`);
    throw error;
  }
}

async function getChurchImage(token, churchId) {
  const url = `https://unityapi.vaumc.org/api/ChurchImage?churchid=${churchId}`;
  try {
    const response = await axios.get(url, {
      headers: { Authorization: `Bearer ${token}` },
      responseType: "json",
    });

    if (response.data && response.data.Image) {
      const base64Image = response.data.Image.split(";base64,").pop();
      if (!response.data.Image || response.data.Image === "Sequence contains no elements") {
        return 0;
      }
      const imageBuffer = Buffer.from(base64Image, "base64");
      return imageBuffer;
    } else {
      return 0;
    }
  } catch (error) {
    console.error("Error fetching church image:", error);
    throw error;
  }
}

async function main() {
  const token = await getToken();
  console.log("got token", token);
  const churchDirectory = await getChurchDirectory(token);
  // let active = 0
  // let closed = 0 
  for (const church of churchDirectory) {
    // church.Status === "Active" ? active++ : closed++ 
    try {
      await insertOrUpdateChurch(church, token);
    } catch (error) {
      console.error("An error occurred:", error);
    }
  }
  console.log("Completed Inserts");
  console.log("Not Updated Churches:", output_not_updated);
  console.log("Successful Updates:", output_success);
  console.log("Errors:", output_error);
}

main();
