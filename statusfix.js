import WPAPI from "wpapi";

let output_not_updated = [];
let output_success = [];
let output_error = [];

const wp = new WPAPI({
    endpoint: "https://vaumc.org/wp-json",
    username: "<EMAIL>",
    password: "ksNl*@z1@Zfc1XBj",
});

// Register custom routes if needed
wp.church = wp.registerRoute("wp/v2", "/church/(?P<id>\\d+)");

// Fetch all churches
async function fetchChurches() {
    let churches = [];
    let page = 1;

    try {
        while (true) {
            const response = await wp.church().param("per_page", 100).param("page", page);
            if (response.length === 0) break;
            churches = churches.concat(response);
            page++;
        }
    } catch (error) {
        console.error("Error fetching churches:", error.message);
    }

    return churches;
}

async function setStatus(id) {
    try {
        wp.church().id(id).update(
           {
            status:"draft"
           } 
        )
        output_success.push({ id: id, message:'post status updated to draft' });
    } catch (error) {
        output_error.push({
            id: id,
            error: error.response?.data || error.message
        })
    }

}


// Main function
async function main() {
    const churches = await fetchChurches();

    for (const church of churches) {
        if(church.acf.Status !== "Active"){
            await setStatus(church.id);
        }
    }
    console.log("Successfully updated:", output_success);
    console.log("No updates needed:", output_not_updated);
    console.error("Errors:", output_error);
}

main();